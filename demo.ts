#!/usr/bin/env tsx

/**
 * OAuth2 MCP 集成演示
 * 
 * 这个演示展示了如何使用符合MCP规范的OAuth2集成：
 * 1. 启动OAuth2授权流程
 * 2. 处理用户授权
 * 3. 交换访问令牌
 * 4. 访问受保护资源
 */

import { spawn, ChildProcess } from 'child_process';
import { setTimeout } from 'timers/promises';

interface ServiceConfig {
  name: string;
  command: string;
  args: string[];
  port: number;
  healthCheck: string;
}

class DemoOrchestrator {
  private services: Map<string, ChildProcess> = new Map();
  
  private serviceConfigs: ServiceConfig[] = [
    {
      name: 'oauth-provider',
      command: 'tsx',
      args: ['src/index.ts'],
      port: 3002,
      healthCheck: 'http://localhost:3002/.well-known/openid_configuration'
    },
    {
      name: 'resource-server',
      command: 'tsx', 
      args: ['src/protected-resource-server.ts'],
      port: 3003,
      healthCheck: 'http://localhost:3003/health'
    },
    {
      name: 'mcp-sse-server',
      command: 'tsx',
      args: ['src/mcp-server-sse.ts'],
      port: 3001,
      healthCheck: 'http://localhost:3001/health'
    }
  ];

  async startServices(): Promise<void> {
    console.log('🚀 启动OAuth2 MCP演示服务...\n');

    for (const config of this.serviceConfigs) {
      console.log(`📡 启动 ${config.name} (端口 ${config.port})...`);
      
      const process = spawn(config.command, config.args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: process.cwd()
      });

      process.stdout?.on('data', (data) => {
        console.log(`[${config.name}] ${data.toString().trim()}`);
      });

      process.stderr?.on('data', (data) => {
        console.error(`[${config.name}] ERROR: ${data.toString().trim()}`);
      });

      process.on('exit', (code) => {
        console.log(`[${config.name}] 进程退出，代码: ${code}`);
      });

      this.services.set(config.name, process);
      
      // 等待服务启动
      await setTimeout(2000);
    }

    console.log('\n✅ 所有服务已启动');
  }

  async checkHealth(): Promise<boolean> {
    console.log('\n🔍 检查服务健康状态...');
    
    let allHealthy = true;
    
    for (const config of this.serviceConfigs) {
      try {
        const response = await fetch(config.healthCheck);
        if (response.ok) {
          console.log(`✅ ${config.name} 健康状态正常`);
        } else {
          console.log(`❌ ${config.name} 健康检查失败: ${response.status}`);
          allHealthy = false;
        }
      } catch (error) {
        console.log(`❌ ${config.name} 无法连接: ${error instanceof Error ? error.message : error}`);
        allHealthy = false;
      }
    }
    
    return allHealthy;
  }

  async stopServices(): Promise<void> {
    console.log('\n🛑 停止所有服务...');
    
    for (const [name, process] of this.services) {
      console.log(`📡 停止 ${name}...`);
      process.kill('SIGTERM');
    }
    
    this.services.clear();
    console.log('✅ 所有服务已停止');
  }

  async runDemo(): Promise<void> {
    console.log('🎭 运行OAuth2 MCP演示...\n');

    // 演示OAuth2流程
    console.log('📋 OAuth2流程演示:');
    console.log('1. 访问授权URL: http://localhost:3002/auth?response_type=code&client_id=oauth2-client-123&redirect_uri=http://localhost:3001/callback&scope=openid%20profile%20email&state=demo_state');
    console.log('2. 完成用户授权');
    console.log('3. 获取授权码并交换访问令牌');
    console.log('4. 使用访问令牌访问受保护资源\n');

    // 演示MCP工具
    console.log('🔧 MCP工具演示:');
    console.log('- oauth2_authorize: 启动授权流程');
    console.log('- oauth2_token_exchange: 交换访问令牌');
    console.log('- oauth2_api_request: 调用受保护API\n');

    // 演示受保护资源
    console.log('🔒 受保护资源演示:');
    console.log('- GET /api/user/profile (需要 profile 作用域)');
    console.log('- GET /api/user/email (需要 email 作用域)');
    console.log('- GET /api/user/info (需要 openid 作用域)');
    console.log('- GET /api/data (通用受保护数据)\n');

    // 演示MCP SSE端点
    console.log('📡 MCP SSE端点:');
    console.log('- http://localhost:3001/sse (MCP Server-Sent Events)');
    console.log('- http://localhost:3001/callback (OAuth2回调)\n');

    console.log('🎉 演示准备就绪！');
    console.log('\n📖 使用说明:');
    console.log('1. 运行 `pnpm run test` 来测试MCP集成');
    console.log('2. 访问授权URL开始OAuth2流程');
    console.log('3. 使用MCP客户端调用OAuth2工具');
    console.log('4. 按 Ctrl+C 停止演示\n');
  }
}

async function main() {
  const orchestrator = new DemoOrchestrator();
  
  // 处理退出信号
  process.on('SIGINT', async () => {
    console.log('\n\n🛑 收到退出信号，正在清理...');
    await orchestrator.stopServices();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    console.log('\n\n🛑 收到终止信号，正在清理...');
    await orchestrator.stopServices();
    process.exit(0);
  });

  try {
    console.log('🎪 OAuth2 MCP 集成演示');
    console.log('=' * 50);
    
    // 启动服务
    await orchestrator.startServices();
    
    // 等待服务完全启动
    await setTimeout(3000);
    
    // 检查健康状态
    const healthy = await orchestrator.checkHealth();
    
    if (!healthy) {
      console.log('\n❌ 部分服务启动失败，请检查日志');
      await orchestrator.stopServices();
      process.exit(1);
    }
    
    // 运行演示
    await orchestrator.runDemo();
    
    // 保持运行状态
    console.log('⏳ 演示正在运行... (按 Ctrl+C 退出)');
    
    // 无限等待，直到用户中断
    while (true) {
      await setTimeout(5000);
      
      // 定期检查服务健康状态
      const stillHealthy = await orchestrator.checkHealth();
      if (!stillHealthy) {
        console.log('\n⚠️  检测到服务异常，请检查日志');
      }
    }
    
  } catch (error) {
    console.error('\n💥 演示失败:', error);
    await orchestrator.stopServices();
    process.exit(1);
  }
}

// 运行演示
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { DemoOrchestrator };
