# OAuth2 MCP Integration

这是一个符合 [MCP (Model Context Protocol) 规范](https://modelcontextprotocol.io/specification/draft/basic/authorization) 的 OAuth2 集成示例。

## 项目结构

```
src/
├── index.ts                    # OAuth2 Provider (OIDC Provider)
├── mcp-oauth-server.ts         # MCP OAuth2 Server (stdio transport)
├── mcp-server-sse.ts          # MCP OAuth2 Server (SSE transport)
├── mcp-client.ts              # MCP Client 示例
├── protected-resource-server.ts # 受保护的资源服务器
└── client/
    └── index.ts               # 原始客户端示例
```

## 功能特性

### 1. 符合 MCP 规范的 OAuth2 实现

- ✅ 授权请求 (Authorization Request)
- ✅ 令牌交换 (Token Exchange)
- ✅ 受保护资源访问 (Protected Resource Access)
- ✅ PKCE 支持 (Proof Key for Code Exchange)
- ✅ 状态验证 (State Verification)
- ✅ 令牌内省 (Token Introspection)

### 2. 多种传输方式

- **stdio**: 标准输入输出传输 (`mcp-oauth-server.ts`)
- **SSE**: Server-Sent Events 传输 (`mcp-server-sse.ts`)

### 3. MCP 工具 (Tools)

- `oauth2_authorize`: 启动 OAuth2 授权流程
- `oauth2_token_exchange`: 交换授权码获取访问令牌
- `oauth2_api_request`: 使用访问令牌进行 API 请求

### 4. MCP 资源 (Resources)

- `oauth2-session://{sessionId}`: 获取 OAuth2 会话信息

## 快速开始

### 1. 安装依赖

```bash
pnpm install
```

### 2. 启动所有服务

```bash
pnpm run demo
```

这将同时启动：

- OAuth2 Provider (端口 3002)
- Protected Resource Server (端口 3003)
- MCP SSE Server (端口 3001)

### 3. 单独启动服务

#### OAuth2 Provider

```bash
pnpm run oauth-provider
```

- 端口: 3002
- 授权端点: http://localhost:3002/auth
- 令牌端点: http://localhost:3002/token

#### 受保护资源服务器

```bash
pnpm run resource-server
```

- 端口: 3003
- API 端点: http://localhost:3003/api/\*

#### MCP Server (stdio)

```bash
pnpm run mcp-server
```

#### MCP Server (SSE)

```bash
pnpm run mcp-server-sse
```

- 端口: 3001
- MCP 端点: http://localhost:3001/sse
- 回调端点: http://localhost:3001/callback

#### MCP Client 示例

```bash
pnpm run mcp-client
```

## OAuth2 流程示例

### 1. 启动授权流程

```typescript
// 使用MCP工具启动授权
const authResult = await client.callTool('oauth2_authorize', {
  usePKCE: true,
})

// 返回结果包含:
// - sessionId: 会话ID
// - authorizationUrl: 授权URL
// - state: 状态参数
```

### 2. 用户授权

用户访问授权 URL，完成授权后会重定向到回调地址，并携带授权码。

### 3. 交换访问令牌

```typescript
// 使用授权码交换访问令牌
const tokenResult = await client.callTool('oauth2_token_exchange', {
  sessionId: 'session_id',
  code: 'authorization_code',
  state: 'state_parameter',
})
```

### 4. 访问受保护资源

```typescript
// 使用访问令牌调用API
const apiResult = await client.callTool('oauth2_api_request', {
  sessionId: 'session_id',
  endpoint: '/api/user/profile',
  method: 'GET',
})
```

## API 端点

### OAuth2 Provider (端口 3002)

- `GET /auth` - 授权端点
- `POST /token` - 令牌端点
- `GET /interaction/:uid` - 用户交互端点

### 受保护资源服务器 (端口 3003)

- `GET /api/user/profile` - 用户资料 (需要 `profile` 作用域)
- `GET /api/user/email` - 用户邮箱 (需要 `email` 作用域)
- `GET /api/user/info` - 用户信息 (需要 `openid` 作用域)
- `GET /api/data` - 受保护数据
- `POST /token/introspect` - 令牌内省 (RFC 7662)
- `POST /token/validate` - 令牌验证
- `GET /health` - 健康检查

### MCP SSE Server (端口 3001)

- `GET /sse` - MCP SSE 端点
- `GET /callback` - OAuth2 回调端点
- `GET /health` - 健康检查

## 配置

### OAuth2 配置

```typescript
const OAUTH_CONFIG = {
  authorizationEndpoint: 'http://localhost:3002/auth',
  tokenEndpoint: 'http://localhost:3002/token',
  clientId: 'oauth2-client-123',
  clientSecret: 'generated_secret',
  redirectUri: 'http://localhost:3001/callback',
  scope: 'openid profile email',
}
```

### 支持的作用域

- `openid`: OpenID Connect 核心作用域
- `profile`: 用户资料信息
- `email`: 用户邮箱信息

## 安全特性

1. **PKCE 支持**: 防止授权码拦截攻击
2. **状态验证**: 防止 CSRF 攻击
3. **令牌过期**: 自动令牌过期管理
4. **作用域验证**: 基于作用域的访问控制
5. **Bearer 令牌**: 标准的令牌传输方式

## 开发和测试

### 构建项目

```bash
pnpm run build
```

### 运行测试

```bash
# 启动所有服务
pnpm run demo

# 在另一个终端运行客户端测试
pnpm run mcp-client
```

## 符合 MCP 规范

此实现符合 MCP 规范的以下要求：

1. **授权流程**: 实现了完整的 OAuth2 授权码流程
2. **令牌管理**: 支持访问令牌和刷新令牌
3. **安全性**: 实现了 PKCE 和状态验证
4. **资源保护**: 基于令牌的 API 访问控制
5. **标准协议**: 遵循 RFC 6749 (OAuth2) 和 RFC 7636 (PKCE)

## 使用示例

### 完整演示

```bash
# 启动完整演示 (推荐)
pnpm run demo

# 或者使用并发方式启动
pnpm run demo:concurrent
```

### 测试 MCP 集成

```bash
# 运行MCP集成测试
pnpm run test
```

### 手动测试 OAuth2 流程

1. **启动服务**

   ```bash
   pnpm run demo
   ```

2. **访问授权 URL**

   ```
   http://localhost:3002/auth?response_type=code&client_id=oauth2-client-123&redirect_uri=http://localhost:3001/callback&scope=openid%20profile%20email&state=demo_state
   ```

3. **完成授权** (使用任意用户名/密码)

4. **获取授权码** (从回调 URL 中提取)

5. **使用 MCP 工具交换令牌**

   ```typescript
   // 使用MCP客户端
   const client = new OAuth2McpClient()
   await client.connect()

   // 交换令牌
   await client.exchangeToken(sessionId, authCode, state)

   // 访问API
   await client.makeApiRequest(sessionId, '/api/user/profile')
   ```

## 注意事项

- 这是一个演示项目，生产环境需要额外的安全措施
- 令牌存储使用内存存储，生产环境应使用数据库或 Redis
- 默认用户凭据在开发模式下可以使用任意用户名/密码
- 所有服务运行在 localhost，生产环境需要配置适当的域名和 HTTPS

## 故障排除

### 常见问题

1. **端口冲突**

   - 确保端口 3001, 3002, 3003 未被占用
   - 可以在配置文件中修改端口

2. **服务启动失败**

   - 检查依赖是否正确安装: `pnpm install`
   - 查看服务日志获取详细错误信息

3. **MCP 连接失败**

   - 确保 MCP 服务器正在运行
   - 检查 stdio/SSE 传输配置

4. **OAuth2 授权失败**
   - 验证客户端 ID 和密钥配置
   - 检查重定向 URI 是否正确

### 调试技巧

- 使用 `pnpm run test` 进行基本功能测试
- 查看各服务的控制台输出
- 使用浏览器开发者工具检查网络请求
- 检查 `mcp-config.json` 配置是否正确
