# Installation
> `npm install --save @types/cookies`

# Summary
This package contains type definitions for cookies (https://github.com/pillarjs/cookies).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cookies.

### Additional Details
 * Last updated: Sat, 07 Jun 2025 02:15:25 GMT
 * Dependencies: [@types/connect](https://npmjs.com/package/@types/connect), [@types/express](https://npmjs.com/package/@types/express), [@types/keygrip](https://npmjs.com/package/@types/keygrip), [@types/node](https://npmjs.com/package/@types/node)

# Credits
These definitions were written by [<PERSON>](https://github.com/<PERSON>), [j<PERSON><PERSON>](https://github.com/jkeylu), [BendingBender](https://github.com/BendingBender), and [<PERSON>](https://github.com/bjohan<PERSON><PERSON>).
