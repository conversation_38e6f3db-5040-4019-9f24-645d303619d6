#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/@modelcontextprotocol+inspector-cli@0.16.1/node_modules/@modelcontextprotocol/inspector-cli/build/node_modules:/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/@modelcontextprotocol+inspector-cli@0.16.1/node_modules/@modelcontextprotocol/inspector-cli/node_modules:/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/@modelcontextprotocol+inspector-cli@0.16.1/node_modules/@modelcontextprotocol/node_modules:/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/@modelcontextprotocol+inspector-cli@0.16.1/node_modules:/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/@modelcontextprotocol+inspector-cli@0.16.1/node_modules/@modelcontextprotocol/inspector-cli/build/node_modules:/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/@modelcontextprotocol+inspector-cli@0.16.1/node_modules/@modelcontextprotocol/inspector-cli/node_modules:/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/@modelcontextprotocol+inspector-cli@0.16.1/node_modules/@modelcontextprotocol/node_modules:/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/@modelcontextprotocol+inspector-cli@0.16.1/node_modules:/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@modelcontextprotocol/inspector-cli/build/cli.js" "$@"
else
  exec node  "$basedir/../@modelcontextprotocol/inspector-cli/build/cli.js" "$@"
fi
