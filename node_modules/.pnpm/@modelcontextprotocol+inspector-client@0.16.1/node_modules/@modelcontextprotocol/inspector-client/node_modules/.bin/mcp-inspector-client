#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/@modelcontextprotocol+inspector-client@0.16.1/node_modules/@modelcontextprotocol/inspector-client/bin/node_modules:/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/@modelcontextprotocol+inspector-client@0.16.1/node_modules/@modelcontextprotocol/inspector-client/node_modules:/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/@modelcontextprotocol+inspector-client@0.16.1/node_modules/@modelcontextprotocol/node_modules:/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/@modelcontextprotocol+inspector-client@0.16.1/node_modules:/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/@modelcontextprotocol+inspector-client@0.16.1/node_modules/@modelcontextprotocol/inspector-client/bin/node_modules:/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/@modelcontextprotocol+inspector-client@0.16.1/node_modules/@modelcontextprotocol/inspector-client/node_modules:/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/@modelcontextprotocol+inspector-client@0.16.1/node_modules/@modelcontextprotocol/node_modules:/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/@modelcontextprotocol+inspector-client@0.16.1/node_modules:/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/start.js" "$@"
else
  exec node  "$basedir/../../bin/start.js" "$@"
fi
