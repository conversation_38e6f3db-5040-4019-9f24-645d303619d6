# Installation
> `npm install --save @types/oidc-provider`

# Summary
This package contains type definitions for oidc-provider (https://github.com/panva/node-oidc-provider).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/oidc-provider.

### Additional Details
 * Last updated: Thu, 19 Jun 2025 20:02:25 GMT
 * Dependencies: [@types/keygrip](https://npmjs.com/package/@types/keygrip), [@types/koa](https://npmjs.com/package/@types/koa), [@types/node](https://npmjs.com/package/@types/node)

# Credits
These definitions were written by [<PERSON><PERSON>](https://github.com/panva).
