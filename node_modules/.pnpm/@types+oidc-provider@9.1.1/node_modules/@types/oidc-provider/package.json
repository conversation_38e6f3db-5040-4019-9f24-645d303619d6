{"name": "@types/oidc-provider", "version": "9.1.1", "description": "TypeScript definitions for oidc-provider", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/oidc-provider", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON>", "url": "https://github.com/panva"}], "type": "module", "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/oidc-provider"}, "scripts": {}, "dependencies": {"@types/keygrip": "*", "@types/koa": "*", "@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "8088796caaf2e10732470facd50c25ef1ca72f2f233087db9cf2520a67f15064", "typeScriptVersion": "5.1"}