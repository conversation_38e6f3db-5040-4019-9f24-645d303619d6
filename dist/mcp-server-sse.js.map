{"version": 3, "file": "mcp-server-sse.js", "sourceRoot": "", "sources": ["../src/mcp-server-sse.ts"], "names": [], "mappings": ";;;;;AAAA,oEAAsF;AACtF,6BAAwB;AACxB,iDAAsC;AACtC,sDAA8B;AAC9B,gDAAwB;AAExB,kDAAkD;AAClD,MAAM,WAAW;IACP,WAAW,GAAkB,IAAI,CAAC;IAClC,QAAQ,GAAQ,IAAI,CAAC;IACrB,SAAS,GAAG,cAAc,CAAC;IAEnC,cAAc,CAAC,KAAa;QAC1B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,qCAAqC;QACrC,OAAO,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IACrD,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,wBAAwB;YACxB,IAAI,CAAC;gBACH,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YACxE,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,qBAAqB;YACvB,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,WAAW;QACf,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACvC,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,0BAA0B,EAAE;oBACvD,OAAO,EAAE;wBACP,eAAe,EAAE,UAAU,IAAI,CAAC,WAAW,EAAE;qBAC9C;iBACF,CAAC,CAAC;gBACH,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACxC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,eAAe;QACb,OAAO,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;IACjC,CAAC;IAED,SAAS;QACP,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC;YACH,OAAO,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,qBAAqB;QACvB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAC9C,MAAM,WAAW,GAAG,IAAA,qBAAK,EAAC,MAAM,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE;YACpD,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,QAAQ;SAChB,CAAC,CAAC;QACH,KAAK,WAAW,CAAC;QAEjB,6BAA6B;QAC7B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,CAAC;CACF;AAED,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;AAEtC,4DAA4D;AAC5D,MAAM,MAAM,GAAG,IAAI,kBAAS,CAAC;IAC3B,IAAI,EAAE,iBAAiB;IACvB,OAAO,EAAE,OAAO;CACjB,EAAE;IACD,YAAY,EAAE;QACZ,aAAa,EAAE;YACb,MAAM,EAAE;gBACN,yCAAyC;gBACzC,gBAAgB,EAAE,8DAA8D;gBAChF,QAAQ,EAAE,6BAA6B;gBACvC,MAAM,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC;aACvC;SACF;KACF;CACF,CAAC,CAAC;AAEH,oCAAoC;AACpC,MAAM,iBAAiB,GAAG,OAAC,CAAC,MAAM,CAAC;IACjC,MAAM,EAAE,OAAC,CAAC,OAAO,CAAC,cAAc,CAAC;IACjC,MAAM,EAAE,OAAC,CAAC,QAAQ,CAAC,OAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;CACjC,CAAC,CAAC;AAEH,MAAM,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;IAClC,MAAM,EAAE,OAAC,CAAC,OAAO,CAAC,eAAe,CAAC;IAClC,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;QACf,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE;KACxB,CAAC;CACH,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,KAAK,IAAI,EAAE;IAC5D,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;IAEjD,uCAAuC;IACvC,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAC;IAExC,OAAO;QACL,gBAAgB,EAAE,6BAA6B;QAC/C,KAAK,EAAE,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE;KACvC,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,KAAK,EAAE,OAAY,EAAE,EAAE;IACzE,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAEhD,MAAM,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC,MAAiC,CAAC;IAElE,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAC9C,CAAC;IAED,yBAAyB;IACzB,WAAW,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;IAExC,OAAO;QACL,OAAO,EAAE,IAAI;KACd,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,uCAAuC;AACvC,MAAM,CAAC,YAAY,CAAC,KAAK,EACvB;IACE,KAAK,EAAE,eAAe;IACtB,WAAW,EAAE,kDAAkD;IAC/D,WAAW,EAAE,EAAE,CAAC,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE;CAC9C,EACD,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;IACjB,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,EAAE,CAAC;QACnC,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAC;IAC1F,CAAC;IAED,MAAM,QAAQ,GAAG,MAAM,WAAW,CAAC,WAAW,EAAE,CAAC;IACjD,MAAM,QAAQ,GAAG,QAAQ,EAAE,IAAI,IAAI,WAAW,CAAC;IAE/C,OAAO;QACL,OAAO,EAAE,CAAC;gBACR,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,SAAS,QAAQ,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;aAClD,CAAC;KACH,CAAC;AACJ,CAAC,CACF,CAAC;AAEF,MAAM,CAAC,YAAY,CAAC,eAAe,EACjC;IACE,KAAK,EAAE,eAAe;IACtB,WAAW,EAAE,oCAAoC;IACjD,WAAW,EAAE,EAAE;CAChB,EACD,KAAK,IAAI,EAAE;IACT,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,EAAE,CAAC;QACnC,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAC;IAC1F,CAAC;IAED,MAAM,QAAQ,GAAG,MAAM,WAAW,CAAC,WAAW,EAAE,CAAC;IACjD,MAAM,KAAK,GAAG,WAAW,CAAC,cAAc,EAAE,CAAC;IAE3C,OAAO;QACL,OAAO,EAAE,CAAC;gBACR,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oBACnB,GAAG,QAAQ;oBACX,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;oBACtC,aAAa,EAAE,IAAI;oBACnB,MAAM,EAAE,iBAAiB;oBACzB,OAAO,EAAE,OAAO;oBAChB,SAAS,EAAE,KAAK;oBAChB,gBAAgB,EAAE,IAAI;oBACtB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC,EAAE,IAAI,EAAE,CAAC,CAAC;aACZ,CAAC;KACH,CAAC;AACJ,CAAC,CACF,CAAC;AAEF,qDAAqD;AACrD,MAAM,CAAC,gBAAgB,CACrB,UAAU,EACV,IAAI,yBAAgB,CAAC,mBAAmB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAC9D;IACE,KAAK,EAAE,mBAAmB;IAC1B,WAAW,EAAE,6DAA6D;CAC3E,EACD,KAAK,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;IACtB,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,EAAE,CAAC;QACnC,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;IACvF,CAAC;IAED,MAAM,QAAQ,GAAG,MAAM,WAAW,CAAC,WAAW,EAAE,CAAC;IACjD,MAAM,QAAQ,GAAG,QAAQ,EAAE,IAAI,IAAI,gBAAgB,CAAC;IAEpD,OAAO;QACL,QAAQ,EAAE,CAAC;gBACT,GAAG,EAAE,GAAG,CAAC,IAAI;gBACb,IAAI,EAAE,UAAU,IAAI,+DAA+D,QAAQ,EAAE;aAC9F,CAAC;KACH,CAAC;AACJ,CAAC,CACF,CAAC;AAEF,8DAA8D;AAC9D,KAAK,UAAU,IAAI;IACjB,MAAM,GAAG,GAAG,IAAA,iBAAO,GAAE,CAAC;IAEtB,mBAAmB;IACnB,GAAG,CAAC,GAAG,CAAC,IAAA,cAAI,EAAC;QACX,MAAM,EAAE,GAAG;QACX,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC;QACnC,cAAc,EAAE,CAAC,cAAc,EAAE,eAAe,CAAC;KAClD,CAAC,CAAC,CAAC;IAEJ,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,IAAI,EAAE,CAAC,CAAC;IAExB,iFAAiF;IACjF,8EAA8E;IAE9E,wBAAwB;IACxB,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QAC9B,GAAG,CAAC,IAAI,CAAC;YACP,MAAM,EAAE,IAAI;YACZ,MAAM,EAAE,gBAAgB;YACxB,gBAAgB,EAAE,IAAI;YACtB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,yDAAyD;IACzD,GAAG,CAAC,GAAG,CAAC,iCAAiC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QACtD,GAAG,CAAC,IAAI,CAAC;YACP,MAAM,EAAE;gBACN,gBAAgB,EAAE,8DAA8D;gBAChF,QAAQ,EAAE,6BAA6B;gBACvC,MAAM,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC;aACvC;YACD,MAAM,EAAE;gBACN,IAAI,EAAE,iBAAiB;gBACvB,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,MAAM;gBACjB,gBAAgB,EAAE,IAAI;aACvB;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,sCAAsC;IACtC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QAChC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;QAC3C,GAAG,CAAC,IAAI,CAAC;YACP,MAAM,EAAE,UAAU;YAClB,OAAO,EAAE,2CAA2C;YACpD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,gBAAgB;IAChB,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QACxB,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,gCAAgC;YACzC,SAAS,EAAE;gBACT,MAAM,EAAE,SAAS;gBACjB,QAAQ,EAAE,iCAAiC;gBAC3C,OAAO,EAAE,UAAU;aACpB;YACD,MAAM,EAAE;gBACN,gBAAgB,EAAE,8DAA8D;gBAChF,SAAS,EAAE,IAAI;aAChB;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,IAAI,GAAG,IAAI,CAAC;IAClB,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE;QACpB,OAAO,CAAC,GAAG,CAAC,yDAAyD,IAAI,EAAE,CAAC,CAAC;QAC7E,OAAO,CAAC,GAAG,CAAC,wCAAwC,IAAI,iCAAiC,CAAC,CAAC;QAC3F,OAAO,CAAC,GAAG,CAAC,uFAAuF,CAAC,CAAC;QACrG,OAAO,CAAC,GAAG,CAAC,gEAAgE,CAAC,CAAC;QAC9E,OAAO,CAAC,GAAG,CAAC,oFAAoF,CAAC,CAAC;IACpG,CAAC,CAAC,CAAC;AACL,CAAC;AAED,IAAI,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC"}