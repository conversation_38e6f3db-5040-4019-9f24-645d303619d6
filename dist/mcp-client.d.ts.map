{"version": 3, "file": "mcp-client.d.ts", "sourceRoot": "", "sources": ["../src/mcp-client.ts"], "names": [], "mappings": "AAGA,qBAAa,gBAAgB;IAC3B,OAAO,CAAC,MAAM,CAAuB;IACrC,OAAO,CAAC,SAAS,CAAqC;IACtD,OAAO,CAAC,SAAS,CAAS;;IAIpB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC;IA4B3B,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAOjC,OAAO,CAAC,OAAO;IAMf,WAAW,IAAI,OAAO;IAIhB,SAAS,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;IAc3B,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,GAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAiB1E,aAAa,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;IAc/B,YAAY,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAcvC,WAAW,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;IAc7B,SAAS,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,GAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAkB3E,eAAe,IAAI,OAAO,CAAC;QAC/B,KAAK,EAAE,GAAG,EAAE,CAAC;QACb,SAAS,EAAE,GAAG,EAAE,CAAC;QACjB,OAAO,EAAE,GAAG,EAAE,CAAC;KAChB,CAAC;IAoBI,cAAc,IAAI,OAAO,CAAC,OAAO,CAAC;CAczC"}