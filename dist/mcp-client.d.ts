export declare class McpClientManager {
    private client;
    private transport;
    private connected;
    constructor();
    connect(): Promise<boolean>;
    disconnect(): Promise<void>;
    private cleanup;
    isConnected(): boolean;
    listTools(): Promise<any[]>;
    callTool(name: string, arguments_?: Record<string, any>): Promise<any>;
    listResources(): Promise<any[]>;
    readResource(uri: string): Promise<any>;
    listPrompts(): Promise<any[]>;
    getPrompt(name: string, arguments_?: Record<string, any>): Promise<any>;
    getCapabilities(): Promise<{
        tools: any[];
        resources: any[];
        prompts: any[];
    }>;
    testConnection(): Promise<boolean>;
}
//# sourceMappingURL=mcp-client.d.ts.map