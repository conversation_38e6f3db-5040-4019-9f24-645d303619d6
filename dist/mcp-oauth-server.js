"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
// Separate HTTP server to provide MCP OAuth2 metadata discovery
// This allows MCP Inspector to discover OAuth2 capabilities even when using stdio transport
const app = (0, express_1.default)();
// Add CORS support
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
        return;
    }
    next();
});
// MCP OAuth2 metadata endpoint
// This tells MCP Inspector where to find OAuth2 authorization server
app.get('/.well-known/mcp-oauth-metadata', (req, res) => {
    res.json({
        // MCP server OAuth2 configuration
        oauth2: {
            authorizationUrl: "http://localhost:3002/.well-known/oauth-authorization-server",
            tokenUrl: "http://localhost:3002/token",
            scopes: ["openid", "profile", "email"]
        },
        // Additional MCP server info
        server: {
            name: "demo-server",
            version: "1.0.0",
            transport: "stdio",
            oauth2_supported: true
        }
    });
});
// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        service: 'mcp-oauth-metadata-server',
        timestamp: new Date().toISOString()
    });
});
// Default route
app.get('/', (req, res) => {
    res.json({
        message: "MCP OAuth2 Metadata Server",
        endpoints: {
            metadata: "/.well-known/mcp-oauth-metadata",
            health: "/health"
        }
    });
});
const port = 8081;
app.listen(port, () => {
    console.log(`🛡️  MCP OAuth2 Metadata Server running on http://localhost:${port}`);
    console.log(`📋 MCP OAuth2 Metadata: http://localhost:${port}/.well-known/mcp-oauth-metadata`);
    console.log(`🔐 Authorization Server Metadata: http://localhost:3002/.well-known/oauth-authorization-server`);
});
//# sourceMappingURL=mcp-oauth-server.js.map