{"version": 3, "file": "mcp-oauth-server.js", "sourceRoot": "", "sources": ["../src/mcp-oauth-server.ts"], "names": [], "mappings": ";;;;;AAAA,sDAA8B;AAE9B,gEAAgE;AAChE,4FAA4F;AAE5F,MAAM,GAAG,GAAG,IAAA,iBAAO,GAAE,CAAC;AAEtB,mBAAmB;AACnB,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;IACzB,GAAG,CAAC,MAAM,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC;IAC/C,GAAG,CAAC,MAAM,CAAC,8BAA8B,EAAE,oBAAoB,CAAC,CAAC;IACjE,GAAG,CAAC,MAAM,CAAC,8BAA8B,EAAE,6BAA6B,CAAC,CAAC;IAC1E,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;QAC7B,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACpB,OAAO;IACT,CAAC;IACD,IAAI,EAAE,CAAC;AACT,CAAC,CAAC,CAAC;AAEH,+BAA+B;AAC/B,qEAAqE;AACrE,GAAG,CAAC,GAAG,CAAC,iCAAiC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;IACtD,GAAG,CAAC,IAAI,CAAC;QACP,kCAAkC;QAClC,MAAM,EAAE;YACN,gBAAgB,EAAE,8DAA8D;YAChF,QAAQ,EAAE,6BAA6B;YACvC,MAAM,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC;SACvC;QACD,6BAA6B;QAC7B,MAAM,EAAE;YACN,IAAI,EAAE,aAAa;YACnB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,OAAO;YAClB,gBAAgB,EAAE,IAAI;SACvB;KACF,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,wBAAwB;AACxB,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;IAC9B,GAAG,CAAC,IAAI,CAAC;QACP,MAAM,EAAE,IAAI;QACZ,OAAO,EAAE,2BAA2B;QACpC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,gBAAgB;AAChB,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;IACxB,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,4BAA4B;QACrC,SAAS,EAAE;YACT,QAAQ,EAAE,iCAAiC;YAC3C,MAAM,EAAE,SAAS;SAClB;KACF,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,MAAM,IAAI,GAAG,IAAI,CAAC;AAClB,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE;IACpB,OAAO,CAAC,GAAG,CAAC,+DAA+D,IAAI,EAAE,CAAC,CAAC;IACnF,OAAO,CAAC,GAAG,CAAC,4CAA4C,IAAI,iCAAiC,CAAC,CAAC;IAC/F,OAAO,CAAC,GAAG,CAAC,gGAAgG,CAAC,CAAC;AAChH,CAAC,CAAC,CAAC"}