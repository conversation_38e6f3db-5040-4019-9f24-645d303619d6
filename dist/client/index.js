"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const hono_1 = require("hono");
const node_server_1 = require("@hono/node-server");
const crypto_1 = require("crypto");
const app = new hono_1.Hono();
// OAuth2 Client Configuration
const CLIENT_ID = 'oauth2-client-123';
const CLIENT_SECRET = (0, crypto_1.createHmac)('sha256', 'secret-key').update(CLIENT_ID).digest('hex');
const REDIRECT_URI = 'http://localhost:3001/callback';
const AUTHORIZATION_ENDPOINT = 'http://localhost:3002/auth';
const TOKEN_ENDPOINT = 'http://localhost:3002/token';
const USERINFO_ENDPOINT = 'http://localhost:3002/me';
// In-memory session storage (use Redis or database in production)
const sessions = {};
// MCP client instances per session
const mcpClients = {};
// Generate random state for CSRF protection
function generateState() {
    return (0, crypto_1.randomBytes)(32).toString('hex');
}
// Generate PKCE code verifier and challenge
function generatePKCE() {
    const codeVerifier = (0, crypto_1.randomBytes)(32).toString('base64url');
    const codeChallenge = (0, crypto_1.createHash)('sha256').update(codeVerifier).digest('base64url');
    return { codeVerifier, codeChallenge };
}
// Home page
app.get('/', (c) => {
    const sessionId = c.req.header('cookie')?.match(/session=([^;]+)/)?.[1];
    const session = sessionId ? sessions[sessionId] : null;
    if (session?.accessToken) {
        const mcpStatus = session.mcpConnected ?
            '<span style="color: green;">✓ Connected</span>' :
            '<span style="color: red;">✗ Not Connected</span>';
        return c.html(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>OAuth2 + MCP Client</title>
          <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
            .info-section { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
            .mcp-section { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0; }
            button { background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
            button:hover { background: #c82333; }
            .mcp-button { background: #007bff; }
            .mcp-button:hover { background: #0056b3; }
            .tool-demo { background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
          </style>
        </head>
        <body>
          <h1>OAuth2 + MCP Client - Authenticated</h1>

          <div class="info-section">
            <h3>User Information:</h3>
            <pre>${JSON.stringify(session.userInfo, null, 2)}</pre>
          </div>

          <div class="info-section">
            <h3>Access Token:</h3>
            <p><code>${session.accessToken.substring(0, 50)}...</code></p>
          </div>

          <div class="mcp-section">
            <h3>MCP Connection Status: ${mcpStatus}</h3>
            ${session.mcpConnected ? `
              <p>✅ Successfully connected to MCP server!</p>
              <div class="tool-demo">
                <h4>Available MCP Tools:</h4>
                <button class="mcp-button" onclick="testMcpTool('add')">Test Addition Tool</button>
                <button class="mcp-button" onclick="testMcpTool('multiply')">Test Multiplication Tool</button>
                <button class="mcp-button" onclick="testMcpTool('get-user-info')">Get User Info</button>
                <button class="mcp-button" onclick="listMcpCapabilities()">List All Capabilities</button>
                <div id="mcp-result" style="margin-top: 10px; padding: 10px; background: white; border-radius: 4px; display: none;"></div>
              </div>
            ` : `
              <p>❌ MCP connection failed. Please try refreshing the page.</p>
            `}
          </div>

          <form method="post" action="/logout">
            <button type="submit">Logout</button>
          </form>

          <script>
            async function testMcpTool(toolName) {
              const resultDiv = document.getElementById('mcp-result');
              resultDiv.style.display = 'block';
              resultDiv.innerHTML = 'Testing tool...';

              try {
                const response = await fetch('/mcp/test-tool', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ tool: toolName })
                });
                const result = await response.json();
                resultDiv.innerHTML = '<h5>Result:</h5><pre>' + JSON.stringify(result, null, 2) + '</pre>';
              } catch (error) {
                resultDiv.innerHTML = '<h5>Error:</h5><p>' + error.message + '</p>';
              }
            }

            async function listMcpCapabilities() {
              const resultDiv = document.getElementById('mcp-result');
              resultDiv.style.display = 'block';
              resultDiv.innerHTML = 'Loading capabilities...';

              try {
                const response = await fetch('/mcp/capabilities');
                const result = await response.json();
                resultDiv.innerHTML = '<h5>MCP Capabilities:</h5><pre>' + JSON.stringify(result, null, 2) + '</pre>';
              } catch (error) {
                resultDiv.innerHTML = '<h5>Error:</h5><p>' + error.message + '</p>';
              }
            }
          </script>
        </body>
      </html>
    `);
    }
    return c.html(`
    <!DOCTYPE html>
    <html>
      <head>
        <title>OAuth2 Client</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
          .login-btn { background: #007bff; color: white; padding: 15px 30px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; text-decoration: none; display: inline-block; }
          .login-btn:hover { background: #0056b3; }
          .info { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <h1>OAuth2 Client Demo</h1>
        <div class="info">
          <p>This is a demo OAuth2 client application. Click the button below to authenticate with the OAuth2 provider.</p>
          <p><strong>Client ID:</strong> ${CLIENT_ID}</p>
          <p><strong>Redirect URI:</strong> ${REDIRECT_URI}</p>
        </div>
        <a href="/login" class="login-btn">Login with OAuth2</a>
      </body>
    </html>
  `);
});
// Initiate OAuth2 flow
app.get('/login', (c) => {
    const state = generateState();
    const { codeVerifier, codeChallenge } = generatePKCE();
    const sessionId = (0, crypto_1.randomBytes)(16).toString('hex');
    // Store state and PKCE verifier in session for verification
    sessions[sessionId] = { state, codeVerifier };
    const authUrl = new URL(AUTHORIZATION_ENDPOINT);
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('client_id', CLIENT_ID);
    authUrl.searchParams.set('redirect_uri', REDIRECT_URI);
    authUrl.searchParams.set('scope', 'openid profile email');
    authUrl.searchParams.set('state', state);
    authUrl.searchParams.set('code_challenge', codeChallenge);
    authUrl.searchParams.set('code_challenge_method', 'S256');
    // Set session cookie
    c.header('Set-Cookie', `session=${sessionId}; HttpOnly; Path=/; Max-Age=3600`);
    return c.redirect(authUrl.toString());
});
// Handle OAuth2 callback
app.get('/callback', async (c) => {
    const code = c.req.query('code');
    const state = c.req.query('state');
    const error = c.req.query('error');
    if (error) {
        return c.text(`OAuth2 Error: ${error}`, 400);
    }
    if (!code || !state) {
        return c.text('Missing authorization code or state', 400);
    }
    const sessionId = c.req.header('cookie')?.match(/session=([^;]+)/)?.[1];
    const session = sessionId ? sessions[sessionId] : null;
    if (!session || session.state !== state) {
        return c.text('Invalid state parameter', 400);
    }
    try {
        // Exchange authorization code for access token
        const tokenResponse = await fetch(TOKEN_ENDPOINT, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Authorization': `Basic ${Buffer.from(`${CLIENT_ID}:${CLIENT_SECRET}`).toString('base64')}`,
            },
            body: new URLSearchParams({
                grant_type: 'authorization_code',
                code,
                redirect_uri: REDIRECT_URI,
                code_verifier: session.codeVerifier,
            }),
        });
        if (!tokenResponse.ok) {
            const errorText = await tokenResponse.text();
            return c.text(`Token exchange failed: ${errorText}`, 400);
        }
        const tokenData = await tokenResponse.json();
        // Get user information
        const userInfoResponse = await fetch(USERINFO_ENDPOINT, {
            headers: {
                'Authorization': `Bearer ${tokenData.access_token}`,
            },
        });
        let userInfo = {};
        if (userInfoResponse.ok) {
            userInfo = await userInfoResponse.json();
        }
        // Store tokens and user info in session
        session.accessToken = tokenData.access_token;
        session.refreshToken = tokenData.refresh_token;
        session.idToken = tokenData.id_token;
        session.userInfo = userInfo;
        // Set authentication token for MCP server
        if (sessionId && session.accessToken) {
            try {
                // Write token to file for MCP server to read
                const fs = require('fs');
                const path = require('path');
                const tokenFile = path.join(process.cwd(), '.mcp-auth-token');
                fs.writeFileSync(tokenFile, session.accessToken);
                console.log(`MCP authentication token set for session ${sessionId}`);
                session.mcpConnected = true;
                // Also store user info for MCP server
                const userInfoFile = path.join(process.cwd(), '.mcp-user-info');
                fs.writeFileSync(userInfoFile, JSON.stringify(userInfo));
            }
            catch (error) {
                console.error('Failed to set MCP authentication token:', error);
                session.mcpConnected = false;
            }
        }
        return c.redirect('/');
    }
    catch (error) {
        console.error('Callback error:', error);
        return c.text('Authentication failed', 500);
    }
});
// MCP test tool endpoint
app.post('/mcp/test-tool', async (c) => {
    const sessionId = c.req.header('cookie')?.match(/session=([^;]+)/)?.[1];
    const session = sessionId ? sessions[sessionId] : null;
    const mcpClient = sessionId ? mcpClients[sessionId] : null;
    if (!session?.accessToken || !mcpClient) {
        return c.json({ error: 'Not authenticated or MCP not connected' }, 401);
    }
    try {
        const { tool } = await c.req.json();
        let result;
        switch (tool) {
            case 'add':
                result = await mcpClient.callTool('add', { a: 5, b: 3 });
                break;
            case 'multiply':
                result = await mcpClient.callTool('multiply', { a: 4, b: 7 });
                break;
            case 'get-user-info':
                result = await mcpClient.callTool('get-user-info');
                break;
            default:
                return c.json({ error: 'Unknown tool' }, 400);
        }
        return c.json({ success: true, result });
    }
    catch (error) {
        console.error('MCP tool test error:', error);
        return c.json({ error: 'Failed to call MCP tool' }, 500);
    }
});
// MCP capabilities endpoint
app.get('/mcp/capabilities', async (c) => {
    const sessionId = c.req.header('cookie')?.match(/session=([^;]+)/)?.[1];
    const session = sessionId ? sessions[sessionId] : null;
    const mcpClient = sessionId ? mcpClients[sessionId] : null;
    if (!session?.accessToken || !mcpClient) {
        return c.json({ error: 'Not authenticated or MCP not connected' }, 401);
    }
    try {
        const capabilities = await mcpClient.getCapabilities();
        return c.json({ success: true, capabilities });
    }
    catch (error) {
        console.error('MCP capabilities error:', error);
        return c.json({ error: 'Failed to get MCP capabilities' }, 500);
    }
});
// Logout
app.post('/logout', (c) => {
    const sessionId = c.req.header('cookie')?.match(/session=([^;]+)/)?.[1];
    if (sessionId && sessions[sessionId]) {
        // Clear MCP authentication token
        try {
            const fs = require('fs');
            const path = require('path');
            const tokenFile = path.join(process.cwd(), '.mcp-auth-token');
            if (fs.existsSync(tokenFile)) {
                fs.unlinkSync(tokenFile);
            }
        }
        catch (error) {
            console.error('Failed to clear MCP token:', error);
        }
        // Disconnect MCP client
        if (mcpClients[sessionId]) {
            mcpClients[sessionId].disconnect();
            delete mcpClients[sessionId];
        }
        delete sessions[sessionId];
    }
    c.header('Set-Cookie', 'session=; HttpOnly; Path=/; Max-Age=0');
    return c.redirect('/');
});
const port = 3001;
console.log(`OAuth2 Client running on http://localhost:${port}`);
(0, node_server_1.serve)({
    fetch: app.fetch,
    port,
});
//# sourceMappingURL=index.js.map