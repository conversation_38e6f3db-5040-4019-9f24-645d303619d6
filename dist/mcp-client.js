"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.McpClientManager = void 0;
const index_js_1 = require("@modelcontextprotocol/sdk/client/index.js");
const stdio_js_1 = require("@modelcontextprotocol/sdk/client/stdio.js");
class McpClientManager {
    client = null;
    transport = null;
    connected = false;
    constructor() { }
    async connect() {
        if (this.connected) {
            return true;
        }
        try {
            this.client = new index_js_1.Client({
                name: "oauth2-mcp-client",
                version: "1.0.0"
            });
            this.transport = new stdio_js_1.StdioClientTransport({
                command: "tsx",
                args: ["src/mcp-server.ts"]
            });
            await this.client.connect(this.transport);
            this.connected = true;
            console.log("Connected to MCP server successfully");
            return true;
        }
        catch (error) {
            console.error("Failed to connect to MCP server:", error);
            this.cleanup();
            return false;
        }
    }
    async disconnect() {
        if (this.transport) {
            await this.transport.close();
        }
        this.cleanup();
    }
    cleanup() {
        this.client = null;
        this.transport = null;
        this.connected = false;
    }
    isConnected() {
        return this.connected;
    }
    async listTools() {
        if (!this.client || !this.connected) {
            throw new Error("MCP client not connected");
        }
        try {
            const result = await this.client.listTools();
            return result.tools || [];
        }
        catch (error) {
            console.error("Failed to list tools:", error);
            throw error;
        }
    }
    async callTool(name, arguments_ = {}) {
        if (!this.client || !this.connected) {
            throw new Error("MCP client not connected");
        }
        try {
            const result = await this.client.callTool({
                name,
                arguments: arguments_
            });
            return result;
        }
        catch (error) {
            console.error(`Failed to call tool ${name}:`, error);
            throw error;
        }
    }
    async listResources() {
        if (!this.client || !this.connected) {
            throw new Error("MCP client not connected");
        }
        try {
            const result = await this.client.listResources();
            return result.resources || [];
        }
        catch (error) {
            console.error("Failed to list resources:", error);
            throw error;
        }
    }
    async readResource(uri) {
        if (!this.client || !this.connected) {
            throw new Error("MCP client not connected");
        }
        try {
            const result = await this.client.readResource({ uri });
            return result;
        }
        catch (error) {
            console.error(`Failed to read resource ${uri}:`, error);
            throw error;
        }
    }
    async listPrompts() {
        if (!this.client || !this.connected) {
            throw new Error("MCP client not connected");
        }
        try {
            const result = await this.client.listPrompts();
            return result.prompts || [];
        }
        catch (error) {
            console.error("Failed to list prompts:", error);
            throw error;
        }
    }
    async getPrompt(name, arguments_ = {}) {
        if (!this.client || !this.connected) {
            throw new Error("MCP client not connected");
        }
        try {
            const result = await this.client.getPrompt({
                name,
                arguments: arguments_
            });
            return result;
        }
        catch (error) {
            console.error(`Failed to get prompt ${name}:`, error);
            throw error;
        }
    }
    // Convenience method to get a summary of all available MCP capabilities
    async getCapabilities() {
        if (!this.client || !this.connected) {
            throw new Error("MCP client not connected");
        }
        try {
            const [tools, resources, prompts] = await Promise.all([
                this.listTools(),
                this.listResources(),
                this.listPrompts()
            ]);
            return { tools, resources, prompts };
        }
        catch (error) {
            console.error("Failed to get capabilities:", error);
            throw error;
        }
    }
    // Method to test the connection with a simple tool call
    async testConnection() {
        if (!this.connected) {
            return false;
        }
        try {
            // Try to call the addition tool as a test
            const result = await this.callTool("add", { a: 2, b: 3 });
            return result && result.content && result.content[0]?.text === "5";
        }
        catch (error) {
            console.error("Connection test failed:", error);
            return false;
        }
    }
}
exports.McpClientManager = McpClientManager;
//# sourceMappingURL=mcp-client.js.map