{"name": "oauth2-auth-flow", "version": "1.0.0", "description": "OAuth2 authentication flow with node-oidc-provider and Hono", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "client": "tsx watch src/client/index.ts"}, "keywords": ["oauth2", "oidc", "hono", "typescript"], "author": "", "license": "MIT", "dependencies": {"@hono/node-server": "^1.12.0", "hono": "^4.8.5", "oidc-provider": "^8.4.6"}, "devDependencies": {"@types/node": "^20.10.0", "tsx": "^4.6.0", "typescript": "^5.3.0"}}