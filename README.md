# OAuth2 Authentication Flow

A complete OAuth2 authentication flow implementation using node-oidc-provider, Hono, and TypeScript.

## Features

- **OAuth2 Provider**: Complete OIDC provider with authorization and token endpoints
- **OAuth2 Client**: Demo client application with full authentication flow
- **Security**: HMAC-based client secrets, CSRF protection with state parameter, PKCE support
- **No Database**: In-memory storage for demo purposes
- **TypeScript**: Full TypeScript support with type safety
- **Modern Stack**: Built with Hono framework and pnpm package manager

## Architecture

### OAuth2 Provider (Port 3002)

- Authorization endpoint: `http://localhost:3002/auth`
- Token endpoint: `http://localhost:3002/token`
- User info endpoint: `http://localhost:3002/me`
- Interactive login: Uses oidc-provider default dev interactions
- Discovery endpoint: `http://localhost:3002/.well-known/openid_configuration`

### OAuth2 Client (Port 3001)

- Client application: `http://localhost:3001`
- Callback endpoint: `http://localhost:3001/callback`
- Client ID: `oauth2-client-123`
- Client Secret: HMAC-SHA256 generated from client ID

## Quick Start

1. **Install dependencies**:

   ```bash
   pnpm install
   ```

2. **Start both services**:

   ```bash
   ./start.sh
   ```

   Or start them separately:

   ```bash
   # Terminal 1 - OAuth2 Provider
   pnpm dev

   # Terminal 2 - OAuth2 Client
   pnpm client
   ```

3. **Test the flow**:
   - Open `http://localhost:3001` in your browser
   - Click "Login with OAuth2"
   - Use any username/password (dev mode accepts any credentials)
   - You'll be redirected back with user information

## OAuth2 Flow

1. **Authorization Request**: Client redirects user to provider's authorization endpoint
2. **User Authentication**: User logs in at the provider's login page
3. **Authorization Grant**: Provider redirects back to client with authorization code
4. **Token Exchange**: Client exchanges authorization code for access token
5. **Resource Access**: Client uses access token to access user information

## Configuration

### Client Configuration

```typescript
const CLIENT_ID = 'oauth2-client-123'
const CLIENT_SECRET = createHmac('sha256', 'secret-key').update(CLIENT_ID).digest('hex')
const REDIRECT_URI = 'http://localhost:3001/callback'
```

### Provider Configuration

- Grant types: `authorization_code`, `refresh_token`
- Response types: `code`
- Scopes: `openid`, `profile`, `email`
- Token TTL: 1 hour (access), 1 day (refresh)
- Interactions: Uses oidc-provider default dev interactions (any credentials work)

## Security Features

- **HMAC Client Secret**: Client secret generated using HMAC-SHA256
- **State Parameter**: CSRF protection using random state values
- **PKCE Support**: Proof Key for Code Exchange for enhanced security
- **Secure Cookies**: HttpOnly session cookies
- **Token Validation**: Proper token exchange and validation

## Development

### Project Structure

```
src/
├── index.ts          # OAuth2 Provider
└── client/
    └── index.ts      # OAuth2 Client
```

### Available Scripts

- `pnpm dev`: Start OAuth2 provider in development mode
- `pnpm client`: Start OAuth2 client in development mode
- `pnpm build`: Build TypeScript to JavaScript
- `pnpm start`: Start built application

## Production Considerations

For production deployment, consider:

1. **Database Integration**: Replace in-memory storage with persistent database
2. **User Management**: Implement proper user authentication and management
3. **Session Storage**: Use Redis or database for session management
4. **HTTPS**: Enable HTTPS for all endpoints
5. **Environment Variables**: Move secrets to environment variables
6. **Rate Limiting**: Add rate limiting for authentication endpoints
7. **Logging**: Implement comprehensive logging and monitoring

## Testing

Test the complete OAuth2 flow:

1. Start both services
2. Navigate to client application
3. Initiate login flow
4. Complete authentication
5. Verify token exchange and user information retrieval

## Dependencies

- **oidc-provider**: OpenID Connect provider implementation
- **hono**: Fast web framework for TypeScript
- **tsx**: TypeScript execution environment
- **crypto**: Node.js crypto module for HMAC generation
