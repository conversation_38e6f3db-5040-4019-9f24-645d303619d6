import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
import { spawn } from "child_process";

class OAuth2McpClient {
  private client: Client;
  private transport: StdioClientTransport;
  private serverProcess: any;

  constructor() {
    this.client = new Client({
      name: "oauth2-mcp-client",
      version: "1.0.0"
    }, {
      capabilities: {
        tools: {},
        resources: {}
      }
    });
  }

  async connect() {
    // Start the MCP server process
    this.serverProcess = spawn("tsx", ["src/mcp-oauth-server.ts"], {
      stdio: ["pipe", "pipe", "inherit"]
    });

    // Create transport using the server process
    this.transport = new StdioClientTransport({
      reader: this.serverProcess.stdout,
      writer: this.serverProcess.stdin
    });

    // Connect to the server
    await this.client.connect(this.transport);
    console.log("Connected to OAuth2 MCP Server");
  }

  async disconnect() {
    if (this.transport) {
      await this.transport.close();
    }
    if (this.serverProcess) {
      this.serverProcess.kill();
    }
  }

  async startAuthorizationFlow(usePKCE: boolean = true) {
    console.log("\n=== Starting OAuth2 Authorization Flow ===");
    
    try {
      const result = await this.client.callTool("oauth2_authorize", {
        usePKCE
      });

      const response = JSON.parse(result.content[0].text);
      console.log("Authorization URL:", response.authorizationUrl);
      console.log("Session ID:", response.sessionId);
      console.log("State:", response.state);
      
      return response;
    } catch (error) {
      console.error("Authorization flow failed:", error);
      throw error;
    }
  }

  async exchangeToken(sessionId: string, code: string, state: string) {
    console.log("\n=== Exchanging Authorization Code for Token ===");
    
    try {
      const result = await this.client.callTool("oauth2_token_exchange", {
        sessionId,
        code,
        state
      });

      const response = JSON.parse(result.content[0].text);
      console.log("Token exchange result:", response);
      
      return response;
    } catch (error) {
      console.error("Token exchange failed:", error);
      throw error;
    }
  }

  async makeApiRequest(sessionId: string, endpoint: string, method: string = "GET") {
    console.log(`\n=== Making API Request: ${method} ${endpoint} ===`);
    
    try {
      const result = await this.client.callTool("oauth2_api_request", {
        sessionId,
        endpoint,
        method
      });

      const response = JSON.parse(result.content[0].text);
      console.log("API Response:", response);
      
      return response;
    } catch (error) {
      console.error("API request failed:", error);
      throw error;
    }
  }

  async getSessionInfo(sessionId: string) {
    console.log("\n=== Getting Session Information ===");
    
    try {
      const result = await this.client.getResource(`oauth2-session://${sessionId}`);
      const response = JSON.parse(result.contents[0].text);
      console.log("Session info:", response);
      
      return response;
    } catch (error) {
      console.error("Failed to get session info:", error);
      throw error;
    }
  }

  async listAvailableTools() {
    console.log("\n=== Available Tools ===");
    
    try {
      const tools = await this.client.listTools();
      tools.tools.forEach(tool => {
        console.log(`- ${tool.name}: ${tool.description}`);
      });
      
      return tools;
    } catch (error) {
      console.error("Failed to list tools:", error);
      throw error;
    }
  }

  async listAvailableResources() {
    console.log("\n=== Available Resources ===");
    
    try {
      const resources = await this.client.listResources();
      resources.resources.forEach(resource => {
        console.log(`- ${resource.name}: ${resource.description}`);
      });
      
      return resources;
    } catch (error) {
      console.error("Failed to list resources:", error);
      throw error;
    }
  }
}

// Demo function
async function runOAuth2Demo() {
  const client = new OAuth2McpClient();
  
  try {
    await client.connect();
    
    // List available capabilities
    await client.listAvailableTools();
    await client.listAvailableResources();
    
    // Start authorization flow
    const authResult = await client.startAuthorizationFlow(true);
    
    console.log("\n" + "=".repeat(60));
    console.log("MANUAL STEP REQUIRED:");
    console.log("1. Open the authorization URL in your browser:");
    console.log("   " + authResult.authorizationUrl);
    console.log("2. Complete the authorization process");
    console.log("3. Copy the authorization code from the callback URL");
    console.log("=".repeat(60));
    
    // Simulate receiving authorization code (in real scenario, this would come from callback)
    const mockAuthCode = "mock_auth_code_12345";
    
    // Exchange code for token
    await client.exchangeToken(authResult.sessionId, mockAuthCode, authResult.state);
    
    // Get session information
    await client.getSessionInfo(authResult.sessionId);
    
    // Make some API requests
    await client.makeApiRequest(authResult.sessionId, "/api/user/profile", "GET");
    await client.makeApiRequest(authResult.sessionId, "/api/user/email", "GET");
    await client.makeApiRequest(authResult.sessionId, "/api/data", "GET");
    
    console.log("\n=== OAuth2 Demo Completed Successfully ===");
    
  } catch (error) {
    console.error("Demo failed:", error);
  } finally {
    await client.disconnect();
  }
}

// Run the demo if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runOAuth2Demo().catch(console.error);
}

export { OAuth2McpClient };
