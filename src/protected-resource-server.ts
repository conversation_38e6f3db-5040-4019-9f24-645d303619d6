import { Hono } from 'hono';
import { serve } from '@hono/node-server';
import { createHmac } from 'crypto';

// Mock token storage (in production, this would be a database or Redis)
const validTokens = new Map<string, {
  userId: string;
  scope: string;
  expiresAt: number;
  clientId: string;
}>();

// Mock user data
const users = {
  'user123': {
    id: 'user123',
    name: '<PERSON>',
    email: '<EMAIL>',
    profile: {
      given_name: '<PERSON>',
      family_name: '<PERSON><PERSON>',
      picture: 'https://example.com/avatar.jpg'
    }
  }
};

const app = new Hono();

// Middleware to validate Bearer token
const authMiddleware = async (c: any, next: any) => {
  const authorization = c.req.header('Authorization');
  
  if (!authorization || !authorization.startsWith('Bearer ')) {
    return c.json({ 
      error: 'unauthorized',
      error_description: 'Missing or invalid authorization header'
    }, 401);
  }

  const token = authorization.substring(7); // Remove "Bearer " prefix
  const tokenInfo = validTokens.get(token);

  if (!tokenInfo) {
    return c.json({
      error: 'invalid_token',
      error_description: 'The access token is invalid or expired'
    }, 401);
  }

  if (Date.now() > tokenInfo.expiresAt) {
    validTokens.delete(token); // Clean up expired token
    return c.json({
      error: 'invalid_token',
      error_description: 'The access token has expired'
    }, 401);
  }

  // Add token info to context
  c.set('tokenInfo', tokenInfo);
  c.set('user', users[tokenInfo.userId as keyof typeof users]);
  
  await next();
};

// Helper function to check scope
const requireScope = (requiredScope: string) => {
  return async (c: any, next: any) => {
    const tokenInfo = c.get('tokenInfo');
    const scopes = tokenInfo.scope.split(' ');
    
    if (!scopes.includes(requiredScope)) {
      return c.json({
        error: 'insufficient_scope',
        error_description: `The request requires scope: ${requiredScope}`
      }, 403);
    }
    
    await next();
  };
};

// Public endpoint for token validation (used by OAuth provider)
app.post('/token/validate', async (c) => {
  const { token } = await c.req.json();
  
  if (!token) {
    return c.json({ error: 'missing_token' }, 400);
  }

  const tokenInfo = validTokens.get(token);
  
  if (!tokenInfo || Date.now() > tokenInfo.expiresAt) {
    return c.json({ valid: false }, 200);
  }

  return c.json({
    valid: true,
    userId: tokenInfo.userId,
    scope: tokenInfo.scope,
    clientId: tokenInfo.clientId,
    expiresAt: tokenInfo.expiresAt
  });
});

// Endpoint to register a token (for demo purposes)
app.post('/token/register', async (c) => {
  const { token, userId, scope, clientId, expiresIn = 3600 } = await c.req.json();
  
  if (!token || !userId || !scope || !clientId) {
    return c.json({ error: 'missing_required_fields' }, 400);
  }

  validTokens.set(token, {
    userId,
    scope,
    clientId,
    expiresAt: Date.now() + (expiresIn * 1000)
  });

  return c.json({ 
    message: 'Token registered successfully',
    expiresAt: Date.now() + (expiresIn * 1000)
  });
});

// Protected endpoints

// User profile endpoint (requires 'profile' scope)
app.get('/api/user/profile', authMiddleware, requireScope('profile'), async (c) => {
  const user = c.get('user');
  
  return c.json({
    id: user.id,
    name: user.name,
    profile: user.profile
  });
});

// User email endpoint (requires 'email' scope)
app.get('/api/user/email', authMiddleware, requireScope('email'), async (c) => {
  const user = c.get('user');
  
  return c.json({
    email: user.email,
    email_verified: true
  });
});

// General user info endpoint (requires 'openid' scope)
app.get('/api/user/info', authMiddleware, requireScope('openid'), async (c) => {
  const user = c.get('user');
  const tokenInfo = c.get('tokenInfo');
  
  return c.json({
    sub: user.id,
    name: user.name,
    email: user.email,
    scope: tokenInfo.scope,
    iat: Math.floor((Date.now() - 3600000) / 1000), // Issued 1 hour ago
    exp: Math.floor(tokenInfo.expiresAt / 1000)
  });
});

// Protected data endpoint
app.get('/api/data', authMiddleware, async (c) => {
  const user = c.get('user');
  const tokenInfo = c.get('tokenInfo');
  
  return c.json({
    message: 'This is protected data',
    user: user.name,
    timestamp: new Date().toISOString(),
    scope: tokenInfo.scope,
    data: [
      { id: 1, title: 'Sample Data 1', value: 'Value 1' },
      { id: 2, title: 'Sample Data 2', value: 'Value 2' },
      { id: 3, title: 'Sample Data 3', value: 'Value 3' }
    ]
  });
});

// Health check endpoint (public)
app.get('/health', async (c) => {
  return c.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'Protected Resource Server'
  });
});

// Token introspection endpoint (RFC 7662)
app.post('/token/introspect', authMiddleware, async (c) => {
  const { token: targetToken } = await c.req.json();
  
  if (!targetToken) {
    return c.json({ active: false });
  }

  const tokenInfo = validTokens.get(targetToken);
  
  if (!tokenInfo || Date.now() > tokenInfo.expiresAt) {
    return c.json({ active: false });
  }

  return c.json({
    active: true,
    scope: tokenInfo.scope,
    client_id: tokenInfo.clientId,
    username: tokenInfo.userId,
    exp: Math.floor(tokenInfo.expiresAt / 1000),
    iat: Math.floor((Date.now() - 3600000) / 1000),
    sub: tokenInfo.userId
  });
});

// Error handler
app.onError((err, c) => {
  console.error('Server error:', err);
  return c.json({
    error: 'internal_server_error',
    error_description: 'An internal server error occurred'
  }, 500);
});

// 404 handler
app.notFound((c) => {
  return c.json({
    error: 'not_found',
    error_description: 'The requested resource was not found'
  }, 404);
});

const port = 3003;

console.log(`Protected Resource Server starting on port ${port}...`);
console.log('Available endpoints:');
console.log('  POST /token/register - Register a token (demo)');
console.log('  POST /token/validate - Validate a token');
console.log('  POST /token/introspect - Introspect a token (RFC 7662)');
console.log('  GET  /api/user/profile - User profile (requires profile scope)');
console.log('  GET  /api/user/email - User email (requires email scope)');
console.log('  GET  /api/user/info - User info (requires openid scope)');
console.log('  GET  /api/data - Protected data');
console.log('  GET  /health - Health check');

serve({
  fetch: app.fetch,
  port
});
