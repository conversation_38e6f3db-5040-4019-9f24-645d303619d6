import { McpServer, ResourceTemplate } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";
import { createHmac, randomBytes } from "crypto";

// OAuth2 configuration
const OAUTH_CONFIG = {
  authorizationEndpoint: "http://localhost:3002/auth",
  tokenEndpoint: "http://localhost:3002/token",
  clientId: "oauth2-client-123",
  clientSecret: createHmac('sha256', 'secret-key').update('oauth2-client-123').digest('hex'),
  redirectUri: "http://localhost:3001/callback",
  scope: "openid profile email"
};

// In-memory storage for demo purposes
const authStorage = new Map<string, {
  state: string;
  codeVerifier?: string;
  accessToken?: string;
  refreshToken?: string;
  expiresAt?: number;
}>();

// Create an MCP server with OAuth2 support
const server = new McpServer({
  name: "oauth2-mcp-server",
  version: "1.0.0"
});

// Helper function to generate random string
function generateRandomString(length: number = 32): string {
  return randomBytes(length).toString('base64url');
}

// Helper function to create authorization URL
function createAuthorizationUrl(state: string, codeVerifier?: string): string {
  const params = new URLSearchParams({
    response_type: 'code',
    client_id: OAUTH_CONFIG.clientId,
    redirect_uri: OAUTH_CONFIG.redirectUri,
    scope: OAUTH_CONFIG.scope,
    state: state
  });

  // Add PKCE parameters if code verifier is provided
  if (codeVerifier) {
    const codeChallenge = createHmac('sha256', codeVerifier).digest('base64url');
    params.append('code_challenge', codeChallenge);
    params.append('code_challenge_method', 'S256');
  }

  return `${OAUTH_CONFIG.authorizationEndpoint}?${params.toString()}`;
}

// Tool: Start OAuth2 authorization flow
server.registerTool("oauth2_authorize",
  {
    title: "OAuth2 Authorization",
    description: "Start OAuth2 authorization flow and get authorization URL",
    inputSchema: {
      type: "object",
      properties: {
        usePKCE: {
          type: "boolean",
          description: "Whether to use PKCE (Proof Key for Code Exchange) for enhanced security",
          default: false
        }
      }
    }
  },
  async ({ usePKCE = false }) => {
    const state = generateRandomString();
    const sessionId = generateRandomString();
    
    let codeVerifier: string | undefined;
    if (usePKCE) {
      codeVerifier = generateRandomString(43); // PKCE requires 43-128 characters
    }

    // Store session data
    authStorage.set(sessionId, {
      state,
      codeVerifier
    });

    const authUrl = createAuthorizationUrl(state, codeVerifier);

    return {
      content: [{
        type: "text",
        text: JSON.stringify({
          sessionId,
          authorizationUrl: authUrl,
          state,
          message: "Please visit the authorization URL to complete the OAuth2 flow"
        }, null, 2)
      }]
    };
  }
);

// Tool: Exchange authorization code for access token
server.registerTool("oauth2_token_exchange",
  {
    title: "OAuth2 Token Exchange",
    description: "Exchange authorization code for access token",
    inputSchema: {
      type: "object",
      properties: {
        sessionId: {
          type: "string",
          description: "Session ID from the authorization step"
        },
        code: {
          type: "string",
          description: "Authorization code received from the callback"
        },
        state: {
          type: "string",
          description: "State parameter to verify the request"
        }
      },
      required: ["sessionId", "code", "state"]
    }
  },
  async ({ sessionId, code, state }) => {
    const session = authStorage.get(sessionId);
    if (!session) {
      throw new Error("Invalid session ID");
    }

    if (session.state !== state) {
      throw new Error("Invalid state parameter - possible CSRF attack");
    }

    // Prepare token request
    const tokenParams = new URLSearchParams({
      grant_type: 'authorization_code',
      code,
      redirect_uri: OAUTH_CONFIG.redirectUri,
      client_id: OAUTH_CONFIG.clientId
    });

    // Add PKCE code verifier if used
    if (session.codeVerifier) {
      tokenParams.append('code_verifier', session.codeVerifier);
    }

    try {
      // Make actual HTTP request to token endpoint
      const response = await fetch(OAUTH_CONFIG.tokenEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${Buffer.from(`${OAUTH_CONFIG.clientId}:${OAUTH_CONFIG.clientSecret}`).toString('base64')}`
        },
        body: tokenParams.toString()
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Token endpoint returned ${response.status}: ${errorData}`);
      }

      const tokenResponse = await response.json();

      // Update session with tokens
      session.accessToken = tokenResponse.access_token;
      session.refreshToken = tokenResponse.refresh_token;
      session.expiresAt = Date.now() + (tokenResponse.expires_in * 1000);
      authStorage.set(sessionId, session);

      // Register token with protected resource server for demo
      if (tokenResponse.access_token) {
        try {
          await fetch('http://localhost:3003/token/register', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              token: tokenResponse.access_token,
              userId: 'user123',
              scope: OAUTH_CONFIG.scope,
              clientId: OAUTH_CONFIG.clientId,
              expiresIn: tokenResponse.expires_in
            })
          });
        } catch (e) {
          console.warn('Failed to register token with resource server:', e);
        }
      }

      return {
        content: [{
          type: "text",
          text: JSON.stringify({
            success: true,
            tokenType: tokenResponse.token_type,
            expiresIn: tokenResponse.expires_in,
            scope: tokenResponse.scope,
            message: "Token exchange successful"
          }, null, 2)
        }]
      };
    } catch (error) {
      throw new Error(`Token exchange failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
);

// Tool: Make authenticated API request
server.registerTool("oauth2_api_request",
  {
    title: "OAuth2 API Request",
    description: "Make an authenticated API request using the access token",
    inputSchema: {
      type: "object",
      properties: {
        sessionId: {
          type: "string",
          description: "Session ID with valid access token"
        },
        endpoint: {
          type: "string",
          description: "API endpoint to call"
        },
        method: {
          type: "string",
          enum: ["GET", "POST", "PUT", "DELETE"],
          default: "GET",
          description: "HTTP method"
        },
        baseUrl: {
          type: "string",
          description: "Base URL for the API (default: http://localhost:3003)",
          default: "http://localhost:3003"
        }
      },
      required: ["sessionId", "endpoint"]
    }
  },
  async ({ sessionId, endpoint, method = "GET", baseUrl = "http://localhost:3003" }) => {
    const session = authStorage.get(sessionId);
    if (!session || !session.accessToken) {
      throw new Error("No valid access token found for this session");
    }

    // Check if token is expired
    if (session.expiresAt && Date.now() > session.expiresAt) {
      throw new Error("Access token has expired");
    }

    try {
      // Make actual API request with Bearer token
      const response = await fetch(`${baseUrl}${endpoint}`, {
        method,
        headers: {
          'Authorization': `Bearer ${session.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      const responseData = await response.json();

      return {
        content: [{
          type: "text",
          text: JSON.stringify({
            status: response.status,
            statusText: response.statusText,
            data: responseData,
            headers: Object.fromEntries(response.headers.entries())
          }, null, 2)
        }]
      };
    } catch (error) {
      throw new Error(`API request failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
);

// Resource: OAuth2 session information
server.registerResource(
  "oauth2-session",
  new ResourceTemplate("oauth2-session://{sessionId}", { list: undefined }),
  {
    title: "OAuth2 Session Resource",
    description: "Get OAuth2 session information and token status"
  },
  async (uri, { sessionId }) => {
    const session = authStorage.get(sessionId);
    if (!session) {
      return {
        contents: [{
          uri: uri.href,
          text: JSON.stringify({ error: "Session not found" }, null, 2)
        }]
      };
    }

    const sessionInfo = {
      sessionId,
      hasAccessToken: !!session.accessToken,
      hasRefreshToken: !!session.refreshToken,
      isExpired: session.expiresAt ? Date.now() > session.expiresAt : false,
      expiresAt: session.expiresAt ? new Date(session.expiresAt).toISOString() : null
    };

    return {
      contents: [{
        uri: uri.href,
        text: JSON.stringify(sessionInfo, null, 2)
      }]
    };
  }
);

// Start the MCP server
const transport = new StdioServerTransport();
await server.connect(transport);

console.log("OAuth2 MCP Server started successfully!");
