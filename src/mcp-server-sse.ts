import { Hono } from 'hono';
import { serve } from '@hono/node-server';
import { McpServer, ResourceTemplate } from "@modelcontextprotocol/sdk/server/mcp.js";
import { SSEServerTransport } from "@modelcontextprotocol/sdk/server/sse.js";
import { z } from "zod";
import { createHmac, randomBytes } from "crypto";

// OAuth2 configuration
const OAUTH_CONFIG = {
  authorizationEndpoint: "http://localhost:3002/auth",
  tokenEndpoint: "http://localhost:3002/token",
  clientId: "oauth2-client-123",
  clientSecret: createHmac('sha256', 'secret-key').update('oauth2-client-123').digest('hex'),
  redirectUri: "http://localhost:3001/callback",
  scope: "openid profile email"
};

// In-memory storage for demo purposes
const authStorage = new Map<string, {
  state: string;
  codeVerifier?: string;
  accessToken?: string;
  refreshToken?: string;
  expiresAt?: number;
}>();

// Helper functions
function generateRandomString(length: number = 32): string {
  return randomBytes(length).toString('base64url');
}

function createAuthorizationUrl(state: string, codeVerifier?: string): string {
  const params = new URLSearchParams({
    response_type: 'code',
    client_id: OAUTH_CONFIG.clientId,
    redirect_uri: OAUTH_CONFIG.redirectUri,
    scope: OAUTH_CONFIG.scope,
    state: state
  });

  if (codeVerifier) {
    const codeChallenge = createHmac('sha256', codeVerifier).digest('base64url');
    params.append('code_challenge', codeChallenge);
    params.append('code_challenge_method', 'S256');
  }

  return `${OAUTH_CONFIG.authorizationEndpoint}?${params.toString()}`;
}

// Create MCP server
const mcpServer = new McpServer({
  name: "oauth2-mcp-sse-server",
  version: "1.0.0"
});

// Register OAuth2 tools (same as stdio version)
mcpServer.registerTool("oauth2_authorize",
  {
    title: "OAuth2 Authorization",
    description: "Start OAuth2 authorization flow and get authorization URL",
    inputSchema: {
      type: "object",
      properties: {
        usePKCE: {
          type: "boolean",
          description: "Whether to use PKCE for enhanced security",
          default: false
        }
      }
    }
  },
  async ({ usePKCE = false }) => {
    const state = generateRandomString();
    const sessionId = generateRandomString();
    
    let codeVerifier: string | undefined;
    if (usePKCE) {
      codeVerifier = generateRandomString(43);
    }

    authStorage.set(sessionId, { state, codeVerifier });
    const authUrl = createAuthorizationUrl(state, codeVerifier);

    return {
      content: [{
        type: "text",
        text: JSON.stringify({
          sessionId,
          authorizationUrl: authUrl,
          state,
          message: "Please visit the authorization URL to complete the OAuth2 flow"
        }, null, 2)
      }]
    };
  }
);

mcpServer.registerTool("oauth2_token_exchange",
  {
    title: "OAuth2 Token Exchange",
    description: "Exchange authorization code for access token",
    inputSchema: {
      type: "object",
      properties: {
        sessionId: { type: "string" },
        code: { type: "string" },
        state: { type: "string" }
      },
      required: ["sessionId", "code", "state"]
    }
  },
  async ({ sessionId, code, state }) => {
    const session = authStorage.get(sessionId);
    if (!session) {
      throw new Error("Invalid session ID");
    }

    if (session.state !== state) {
      throw new Error("Invalid state parameter");
    }

    const tokenParams = new URLSearchParams({
      grant_type: 'authorization_code',
      code,
      redirect_uri: OAUTH_CONFIG.redirectUri,
      client_id: OAUTH_CONFIG.clientId
    });

    if (session.codeVerifier) {
      tokenParams.append('code_verifier', session.codeVerifier);
    }

    try {
      const response = await fetch(OAUTH_CONFIG.tokenEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${Buffer.from(`${OAUTH_CONFIG.clientId}:${OAUTH_CONFIG.clientSecret}`).toString('base64')}`
        },
        body: tokenParams.toString()
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Token endpoint returned ${response.status}: ${errorData}`);
      }

      const tokenResponse = await response.json();

      session.accessToken = tokenResponse.access_token;
      session.refreshToken = tokenResponse.refresh_token;
      session.expiresAt = Date.now() + (tokenResponse.expires_in * 1000);
      authStorage.set(sessionId, session);

      // Register token with protected resource server
      try {
        await fetch('http://localhost:3003/token/register', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            token: tokenResponse.access_token,
            userId: 'user123',
            scope: OAUTH_CONFIG.scope,
            clientId: OAUTH_CONFIG.clientId,
            expiresIn: tokenResponse.expires_in
          })
        });
      } catch (e) {
        console.warn('Failed to register token with resource server:', e);
      }

      return {
        content: [{
          type: "text",
          text: JSON.stringify({
            success: true,
            tokenType: tokenResponse.token_type,
            expiresIn: tokenResponse.expires_in,
            scope: tokenResponse.scope,
            message: "Token exchange successful"
          }, null, 2)
        }]
      };
    } catch (error) {
      throw new Error(`Token exchange failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
);

mcpServer.registerTool("oauth2_api_request",
  {
    title: "OAuth2 API Request",
    description: "Make an authenticated API request",
    inputSchema: {
      type: "object",
      properties: {
        sessionId: { type: "string" },
        endpoint: { type: "string" },
        method: { type: "string", enum: ["GET", "POST", "PUT", "DELETE"], default: "GET" },
        baseUrl: { type: "string", default: "http://localhost:3003" }
      },
      required: ["sessionId", "endpoint"]
    }
  },
  async ({ sessionId, endpoint, method = "GET", baseUrl = "http://localhost:3003" }) => {
    const session = authStorage.get(sessionId);
    if (!session || !session.accessToken) {
      throw new Error("No valid access token found");
    }

    if (session.expiresAt && Date.now() > session.expiresAt) {
      throw new Error("Access token has expired");
    }

    try {
      const response = await fetch(`${baseUrl}${endpoint}`, {
        method,
        headers: {
          'Authorization': `Bearer ${session.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      const responseData = await response.json();

      return {
        content: [{
          type: "text",
          text: JSON.stringify({
            status: response.status,
            statusText: response.statusText,
            data: responseData
          }, null, 2)
        }]
      };
    } catch (error) {
      throw new Error(`API request failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
);

// Register OAuth2 session resource
mcpServer.registerResource(
  "oauth2-session",
  new ResourceTemplate("oauth2-session://{sessionId}", { list: undefined }),
  {
    title: "OAuth2 Session Resource",
    description: "Get OAuth2 session information"
  },
  async (uri, { sessionId }) => {
    const session = authStorage.get(sessionId);
    if (!session) {
      return {
        contents: [{
          uri: uri.href,
          text: JSON.stringify({ error: "Session not found" }, null, 2)
        }]
      };
    }

    const sessionInfo = {
      sessionId,
      hasAccessToken: !!session.accessToken,
      hasRefreshToken: !!session.refreshToken,
      isExpired: session.expiresAt ? Date.now() > session.expiresAt : false,
      expiresAt: session.expiresAt ? new Date(session.expiresAt).toISOString() : null
    };

    return {
      contents: [{
        uri: uri.href,
        text: JSON.stringify(sessionInfo, null, 2)
      }]
    };
  }
);

// Create Hono app for HTTP endpoints
const app = new Hono();

// CORS middleware
app.use('*', async (c, next) => {
  c.header('Access-Control-Allow-Origin', '*');
  c.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  c.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (c.req.method === 'OPTIONS') {
    return c.text('', 200);
  }
  
  await next();
});

// MCP SSE endpoint
app.get('/sse', async (c) => {
  const transport = new SSEServerTransport("/sse", c.res);
  await mcpServer.connect(transport);
  return transport.response;
});

// OAuth2 callback endpoint
app.get('/callback', async (c) => {
  const code = c.req.query('code');
  const state = c.req.query('state');
  const error = c.req.query('error');

  if (error) {
    return c.html(`
      <html>
        <body>
          <h1>Authorization Error</h1>
          <p>Error: ${error}</p>
          <p>Description: ${c.req.query('error_description') || 'Unknown error'}</p>
        </body>
      </html>
    `);
  }

  if (!code || !state) {
    return c.html(`
      <html>
        <body>
          <h1>Authorization Error</h1>
          <p>Missing authorization code or state parameter</p>
        </body>
      </html>
    `);
  }

  return c.html(`
    <html>
      <body>
        <h1>Authorization Successful</h1>
        <p>Authorization code: <code>${code}</code></p>
        <p>State: <code>${state}</code></p>
        <p>You can now use these values to exchange for an access token.</p>
        <script>
          // Auto-close window after 3 seconds
          setTimeout(() => window.close(), 3000);
        </script>
      </body>
    </html>
  `);
});

// Health check
app.get('/health', (c) => {
  return c.json({
    status: 'healthy',
    service: 'OAuth2 MCP SSE Server',
    timestamp: new Date().toISOString()
  });
});

const port = 3001;

console.log(`OAuth2 MCP SSE Server starting on port ${port}...`);
console.log(`MCP SSE endpoint: http://localhost:${port}/sse`);
console.log(`OAuth2 callback: http://localhost:${port}/callback`);
console.log(`Health check: http://localhost:${port}/health`);

serve({
  fetch: app.fetch,
  port
});
