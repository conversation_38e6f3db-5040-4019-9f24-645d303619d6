import { Hono } from 'hono';
import { serve } from '@hono/node-server';
import { createHmac, randomBytes, createHash } from 'crypto';

const app = new Hono();

// OAuth2 Client Configuration
const CLIENT_ID = 'oauth2-client-123';
const CLIENT_SECRET = createHmac('sha256', 'secret-key').update(CLIENT_ID).digest('hex');
const REDIRECT_URI = 'http://localhost:3001/callback';
const AUTHORIZATION_ENDPOINT = 'http://localhost:3002/auth';
const TOKEN_ENDPOINT = 'http://localhost:3002/token';
const USERINFO_ENDPOINT = 'http://localhost:3002/me';

// In-memory session storage (use Redis or database in production)
const sessions: Record<string, any> = {};

// Generate random state for CSRF protection
function generateState(): string {
  return randomBytes(32).toString('hex');
}

// Generate PKCE code verifier and challenge
function generatePKCE() {
  const codeVerifier = randomBytes(32).toString('base64url');
  const codeChallenge = createHash('sha256').update(codeVerifier).digest('base64url');
  return { codeVerifier, codeChallenge };
}

// Home page
app.get('/', (c) => {
  const sessionId = c.req.header('cookie')?.match(/session=([^;]+)/)?.[1];
  const session = sessionId ? sessions[sessionId] : null;

  if (session?.accessToken) {
    return c.html(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>OAuth2 Client</title>
          <style>
            body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
            .user-info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
            button { background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
            button:hover { background: #c82333; }
          </style>
        </head>
        <body>
          <h1>OAuth2 Client - Authenticated</h1>
          <div class="user-info">
            <h3>User Information:</h3>
            <pre>${JSON.stringify(session.userInfo, null, 2)}</pre>
          </div>
          <div class="user-info">
            <h3>Access Token:</h3>
            <p><code>${session.accessToken}</code></p>
          </div>
          <form method="post" action="/logout">
            <button type="submit">Logout</button>
          </form>
        </body>
      </html>
    `);
  }

  return c.html(`
    <!DOCTYPE html>
    <html>
      <head>
        <title>OAuth2 Client</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
          .login-btn { background: #007bff; color: white; padding: 15px 30px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; text-decoration: none; display: inline-block; }
          .login-btn:hover { background: #0056b3; }
          .info { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <h1>OAuth2 Client Demo</h1>
        <div class="info">
          <p>This is a demo OAuth2 client application. Click the button below to authenticate with the OAuth2 provider.</p>
          <p><strong>Client ID:</strong> ${CLIENT_ID}</p>
          <p><strong>Redirect URI:</strong> ${REDIRECT_URI}</p>
        </div>
        <a href="/login" class="login-btn">Login with OAuth2</a>
      </body>
    </html>
  `);
});

// Initiate OAuth2 flow
app.get('/login', (c) => {
  const state = generateState();
  const { codeVerifier, codeChallenge } = generatePKCE();
  const sessionId = randomBytes(16).toString('hex');

  // Store state and PKCE verifier in session for verification
  sessions[sessionId] = { state, codeVerifier };

  const authUrl = new URL(AUTHORIZATION_ENDPOINT);
  authUrl.searchParams.set('response_type', 'code');
  authUrl.searchParams.set('client_id', CLIENT_ID);
  authUrl.searchParams.set('redirect_uri', REDIRECT_URI);
  authUrl.searchParams.set('scope', 'openid profile email');
  authUrl.searchParams.set('state', state);
  authUrl.searchParams.set('code_challenge', codeChallenge);
  authUrl.searchParams.set('code_challenge_method', 'S256');

  // Set session cookie
  c.header('Set-Cookie', `session=${sessionId}; HttpOnly; Path=/; Max-Age=3600`);

  return c.redirect(authUrl.toString());
});

// Handle OAuth2 callback
app.get('/callback', async (c) => {
  const code = c.req.query('code');
  const state = c.req.query('state');
  const error = c.req.query('error');
  
  if (error) {
    return c.text(`OAuth2 Error: ${error}`, 400);
  }
  
  if (!code || !state) {
    return c.text('Missing authorization code or state', 400);
  }
  
  const sessionId = c.req.header('cookie')?.match(/session=([^;]+)/)?.[1];
  const session = sessionId ? sessions[sessionId] : null;
  
  if (!session || session.state !== state) {
    return c.text('Invalid state parameter', 400);
  }
  
  try {
    // Exchange authorization code for access token
    const tokenResponse = await fetch(TOKEN_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${Buffer.from(`${CLIENT_ID}:${CLIENT_SECRET}`).toString('base64')}`,
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        redirect_uri: REDIRECT_URI,
        code_verifier: session.codeVerifier,
      }),
    });
    
    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      return c.text(`Token exchange failed: ${errorText}`, 400);
    }
    
    const tokenData = await tokenResponse.json();
    
    // Get user information
    const userInfoResponse = await fetch(USERINFO_ENDPOINT, {
      headers: {
        'Authorization': `Bearer ${tokenData.access_token}`,
      },
    });
    
    let userInfo = {};
    if (userInfoResponse.ok) {
      userInfo = await userInfoResponse.json();
    }
    
    // Store tokens and user info in session
    session.accessToken = tokenData.access_token;
    session.refreshToken = tokenData.refresh_token;
    session.idToken = tokenData.id_token;
    session.userInfo = userInfo;
    
    return c.redirect('/');
  } catch (error) {
    console.error('Callback error:', error);
    return c.text('Authentication failed', 500);
  }
});

// Logout
app.post('/logout', (c) => {
  const sessionId = c.req.header('cookie')?.match(/session=([^;]+)/)?.[1];
  if (sessionId && sessions[sessionId]) {
    delete sessions[sessionId];
  }
  
  c.header('Set-Cookie', 'session=; HttpOnly; Path=/; Max-Age=0');
  return c.redirect('/');
});

const port = 3001;
console.log(`OAuth2 Client running on http://localhost:${port}`);

serve({
  fetch: app.fetch,
  port,
});
