{"mcpServers": {"oauth2-mcp-server": {"command": "tsx", "args": ["src/mcp-oauth-server.ts"], "cwd": ".", "description": "OAuth2 MCP Server with stdio transport", "capabilities": {"tools": [{"name": "oauth2_authorize", "description": "Start OAuth2 authorization flow and get authorization URL"}, {"name": "oauth2_token_exchange", "description": "Exchange authorization code for access token"}, {"name": "oauth2_api_request", "description": "Make an authenticated API request using the access token"}], "resources": [{"name": "oauth2-session", "description": "Get OAuth2 session information and token status", "template": "oauth2-session://{sessionId}"}]}, "env": {"NODE_ENV": "development"}}, "oauth2-mcp-sse-server": {"url": "http://localhost:3001/sse", "description": "OAuth2 MCP Server with SSE transport", "transport": "sse", "capabilities": {"tools": [{"name": "oauth2_authorize", "description": "Start OAuth2 authorization flow and get authorization URL"}, {"name": "oauth2_token_exchange", "description": "Exchange authorization code for access token"}, {"name": "oauth2_api_request", "description": "Make an authenticated API request using the access token"}], "resources": [{"name": "oauth2-session", "description": "Get OAuth2 session information and token status", "template": "oauth2-session://{sessionId}"}]}}}, "oauth2": {"provider": {"name": "Demo OAuth2 Provider", "authorizationEndpoint": "http://localhost:3002/auth", "tokenEndpoint": "http://localhost:3002/token", "userInfoEndpoint": "http://localhost:3003/api/user/info", "introspectionEndpoint": "http://localhost:3003/token/introspect", "issuer": "http://localhost:3002", "scopes": ["openid", "profile", "email"], "responseTypes": ["code"], "grantTypes": ["authorization_code", "refresh_token"], "tokenEndpointAuthMethods": ["client_secret_basic", "client_secret_post"], "codeChallengeMethodsSupported": ["S256"], "pkceRequired": true}, "client": {"clientId": "oauth2-client-123", "clientSecret": "generated_secret_hash", "redirectUris": ["http://localhost:3001/callback"], "scope": "openid profile email", "responseType": "code", "grantType": "authorization_code", "tokenEndpointAuthMethod": "client_secret_basic"}, "resourceServer": {"baseUrl": "http://localhost:3003", "endpoints": {"userProfile": "/api/user/profile", "userEmail": "/api/user/email", "userInfo": "/api/user/info", "protectedData": "/api/data", "tokenIntrospection": "/token/introspect", "tokenValidation": "/token/validate", "health": "/health"}, "requiredScopes": {"/api/user/profile": ["profile"], "/api/user/email": ["email"], "/api/user/info": ["openid"], "/api/data": []}}}, "security": {"pkce": {"enabled": true, "method": "S256", "codeVerifierLength": 43}, "state": {"enabled": true, "length": 32}, "tokenValidation": {"validateExpiry": true, "validateScope": true, "validateIssuer": true}}, "development": {"autoStartServices": true, "services": [{"name": "oauth-provider", "command": "tsx src/index.ts", "port": 3002, "healthCheck": "http://localhost:3002/.well-known/openid_configuration"}, {"name": "resource-server", "command": "tsx src/protected-resource-server.ts", "port": 3003, "healthCheck": "http://localhost:3003/health"}, {"name": "mcp-sse-server", "command": "tsx src/mcp-server-sse.ts", "port": 3001, "healthCheck": "http://localhost:3001/health"}], "testUsers": [{"id": "user123", "username": "testuser", "password": "testpass", "profile": {"name": "<PERSON>", "email": "<EMAIL>", "given_name": "<PERSON>", "family_name": "<PERSON><PERSON>"}}]}, "logging": {"level": "info", "enableRequestLogging": true, "enableTokenLogging": false, "enableErrorLogging": true}}